# process_test.py

import time
import asyncio
import httpx
from datetime import datetime, timezone
from fastapi import APIRouter, HTTPException, UploadFile, Form, File, Request

from services.processors.smarthr.cv_processor_test import CVProcessorTest
from services.processors.papirus.tutela_contestacion_processor import TutelaContestacionProcessor
from services.processors.papirus.tutela_fallo_processor import TutelaFalloProcessor
from services.processors.papirus.tutela_desacato_processor import TutelaDesacatoProcessor
from services.processors.papirus.tutela_correo_processor import TutelaCorreoProcessor
from services.processors.facturius.invoice_processor_test import InvoiceProcessorTest
from routes.task_management import store_task, update_task_status

router = APIRouter()

def get_processor_mapping(openai_client, langchain_client):
    return {
        "invoice": InvoiceProcessorTest(openai_client, langchain_client),
        "cv": CVProcessorTest(openai_client, langchain_client),
        "tutela_contestacion": TutelaContestacionProcessor(openai_client, langchain_client),
        "tutela_correo_contestacion": TutelaCorreoProcessor(openai_client, langchain_client),
        "tutela_fallo": TutelaFalloProcessor(openai_client, langchain_client),
        "tutela_correo_fallo": TutelaCorreoProcessor(openai_client, langchain_client),
        "tutela_desacato": TutelaDesacatoProcessor(openai_client, langchain_client),
        "tutela_correo_desacato": TutelaCorreoProcessor(openai_client, langchain_client),
        "tutela_correo": TutelaCorreoProcessor(openai_client, langchain_client),
    }

@router.post("/process-test", summary="Process a document asynchronously with callback")
async def process_test_request(
    request: Request,
    action: str = Form(...),
    callback_url: str = Form(...),
    file: UploadFile = File(None),
    data: str = Form(None)
):
    logger = request.app.state.logger
    logger.info(f"Async process-test request for action: {action}")

    if not file and not data:
        raise HTTPException(status_code=400, detail="A file or text is required.")
    if file and data:
        raise HTTPException(status_code=400, detail="Send either a text or a file, not both.")

    openai_client = request.app.state.openai_client
    langchain_client = request.app.state.langchain_client

    processor_mapping = get_processor_mapping(openai_client, langchain_client)
    processor = processor_mapping.get(action.lower())

    if not processor:
        raise HTTPException(status_code=400, detail="Invalid Action.")

    # Generate unique task ID following the established pattern
    task_id = f"{action.lower()}-{int(time.time())}-{id(processor)}"
    logger.info(f"Generated task ID: {task_id}")

    # Store initial task information
    file_name = getattr(file, "filename", None) if file else str(data)[:50] if data else "No file"
    store_task(
        task_id=task_id,
        action=action.lower(),
        status="processing",
        file_name=file_name,
        callback_url=callback_url
    )

    async def run_and_callback():
        start_time = time.time()
        try:
            result = await processor.process(file, data)
            processing_time = time.time() - start_time

            # Create success payload with task ID and standard webhook format
            success_payload = {
                "task_id": task_id,
                "status": "completed",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "data": result,
                "processing_time": processing_time
            }

            # Update task status to completed
            update_task_status(
                task_id=task_id,
                status="completed",
                result=result,
                processing_time=processing_time,
                completed_at=time.time()
            )

            async with httpx.AsyncClient(timeout=10.0) as client:
                await client.post(callback_url, json=success_payload)
            logger.info(f"Callback to {callback_url} successful for task {task_id}.")

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Error in async process-test for task {task_id}: {e}", exc_info=True)

            # Create error payload with task ID and standard webhook format
            error_payload = {
                "task_id": task_id,
                "status": "failed",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "data": {"error": str(e)},
                "processing_time": processing_time
            }

            # Update task status to failed
            update_task_status(
                task_id=task_id,
                status="failed",
                error=str(e),
                processing_time=processing_time,
                failed_at=time.time()
            )

            try:
                async with httpx.AsyncClient(timeout=10.0) as client:
                    await client.post(callback_url, json=error_payload)
                logger.info(f"Error callback to {callback_url} successful for task {task_id}.")
            except Exception as cb_err:
                logger.error(f"Callback delivery failed for task {task_id}: {cb_err}", exc_info=True)

    asyncio.create_task(run_and_callback())

    # Return immediate response with task ID
    return {
        "status": "accepted",
        "message": "Processing started",
        "task_id": task_id,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
