{"info": {"name": "LumusAI Background Processing Tests", "description": "Test collection to prove background processing eliminates timeout issues", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "task_id", "value": "", "type": "string"}], "item": [{"name": "1. Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check if the service is running and healthy"}, "response": []}, {"name": "2. <PERSON> Webhook Endpoint", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"task_id\": \"postman-test-123\",\n  \"status\": \"completed\",\n  \"timestamp\": \"2024-01-15T10:30:00Z\",\n  \"data\": {\n    \"test\": true,\n    \"source\": \"postman\"\n  },\n  \"processing_time\": 1.5\n}"}, "url": {"raw": "{{base_url}}/webhook/test", "host": ["{{base_url}}"], "path": ["webhook", "test"]}, "description": "Test the webhook endpoint to ensure it's working"}, "response": []}, {"name": "3. 🎯 MAIN TEST: Background Processing (Text Data)", "event": [{"listen": "test", "script": {"exec": ["// Test that proves no timeout issues", "pm.test('Response time is less than 5 seconds (PROOF: No timeout!)', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains task_id', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('task_id');", "    pm.expect(jsonData.task_id).to.be.a('string');", "    ", "    // Save task_id for other requests", "    pm.collectionVariables.set('task_id', jsonData.task_id);", "});", "", "pm.test('Status is processing', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql('processing');", "});", "", "pm.test('Contains status and result URLs', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('check_status');", "    pm.expect(jsonData).to.have.property('get_result');", "});", "", "// Log the proof", "console.log('🎉 PROOF: Request completed in ' + pm.response.responseTime + 'ms');", "console.log('✅ Task ID: ' + pm.response.json().task_id);", "console.log('🚀 Background processing started - no timeout issues!');"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "action", "value": "cv", "type": "text"}, {"key": "data", "value": "<PERSON>\\nSoftware Engineer\\nEmail: <EMAIL>\\nPhone: ******-0123\\n\\nEXPERIENCE:\\nSenior Software Engineer at Tech Corp (2020-2024)\\n- Developed Python applications with FastAPI\\n- Worked with Docker and microservices\\n- Led a team of 5 developers\\n\\nSoftware Engineer at StartupXYZ (2018-2020)\\n- Built web applications using JavaScript and React\\n- Implemented REST APIs\\n- Worked with PostgreSQL databases\\n\\nEDUCATION:\\nBachelor of Computer Science\\nUniversity of Technology (2014-2018)\\n\\nSKILLS:\\n- Python (5 years experience)\\n- JavaScript (4 years experience)\\n- Docker (3 years experience)\\n- FastAPI (2 years experience)\\n- React (3 years experience)", "type": "text"}, {"key": "background", "value": "true", "type": "text"}, {"key": "webhook_url", "value": "{{base_url}}/webhook/test", "type": "text"}]}, "url": {"raw": "{{base_url}}/process", "host": ["{{base_url}}"], "path": ["process"]}, "description": "🎯 MAIN PROOF: This request should return immediately (< 5 seconds) with a task_id, proving there are no timeout issues!"}, "response": []}, {"name": "4. Check Task Status", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains task info', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('task_id');", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData).to.have.property('action');", "});", "", "// Log current status", "const jsonData = pm.response.json();", "console.log('📊 Task Status: ' + jsonData.status);", "console.log('⏱️  Created: ' + new Date(jsonData.created_at * 1000).toLocaleString());"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/task/status/{{task_id}}", "host": ["{{base_url}}"], "path": ["task", "status", "{{task_id}}"]}, "description": "Check the status of the background task. Run this multiple times to see progress."}, "response": []}, {"name": "5. Get Task Result (When Ready)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    pm.test('Task completed successfully', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('result');", "        pm.expect(jsonData.status).to.eql('completed');", "    });", "    ", "    pm.test('Result contains processing time', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('processing_time');", "        pm.expect(jsonData.processing_time).to.be.a('number');", "    });", "    ", "    // Log success", "    const jsonData = pm.response.json();", "    console.log('🎉 Task completed successfully!');", "    console.log('⏱️  Processing time: ' + jsonData.processing_time + ' seconds');", "    ", "    if (jsonData.result && typeof jsonData.result === 'object') {", "        console.log('📋 Result keys: ' + Object.keys(jsonData.result).join(', '));", "    }", "} else if (pm.response.code === 202) {", "    pm.test('Task still processing', function () {", "        pm.expect(pm.response.text()).to.include('still processing');", "    });", "    console.log('⏳ Task is still processing... try again in a few minutes');", "} else {", "    console.log('❌ Unexpected response code: ' + pm.response.code);", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/task/result/{{task_id}}", "host": ["{{base_url}}"], "path": ["task", "result", "{{task_id}}"]}, "description": "Get the final result of the background task. This will return 202 if still processing, or 200 with results when complete."}, "response": []}, {"name": "6. List All Tasks", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains task counts', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('active_count');", "    pm.expect(jsonData).to.have.property('completed_count');", "    pm.expect(jsonData).to.have.property('total_count');", "});", "", "// Log task summary", "const jsonData = pm.response.json();", "console.log('📊 Task Summary:');", "console.log('   Active: ' + jsonData.active_count);", "console.log('   Completed: ' + jsonData.completed_count);", "console.log('   Total: ' + jsonData.total_count);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/tasks/list", "host": ["{{base_url}}"], "path": ["tasks", "list"]}, "description": "List all background tasks for monitoring"}, "response": []}, {"name": "7. 🔄 COMPARISON: Normal Processing (Takes 5-6 minutes)", "event": [{"listen": "test", "script": {"exec": ["// This test shows the difference", "pm.test('Normal processing completed (but took a long time)', function () {", "    pm.response.to.have.status(200);", "    console.log('⏱️  Normal processing took: ' + pm.response.responseTime + 'ms');", "    console.log('🔄 Compare this to background processing which returns immediately!');", "});", "", "if (pm.response.responseTime > 10000) {", "    console.log('⚠️  This request took over 10 seconds - this is why we need background processing!');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "action", "value": "cv", "type": "text"}, {"key": "data", "value": "Simple test CV: <PERSON>, <PERSON><PERSON><PERSON>, Python experience", "type": "text"}]}, "url": {"raw": "{{base_url}}/process", "host": ["{{base_url}}"], "path": ["process"]}, "description": "⚠️ WARNING: This is normal processing that will take 5-6 minutes. Only run this to compare with background processing. This shows why background processing is needed!"}, "response": []}, {"name": "8. 📁 Background Processing with File Upload", "event": [{"listen": "test", "script": {"exec": ["pm.test('File upload background processing works', function () {", "    pm.response.to.have.status(200);", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Returns task_id for file processing', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('task_id');", "    pm.collectionVariables.set('file_task_id', jsonData.task_id);", "});", "", "console.log('📁 File processing task started: ' + pm.response.json().task_id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "action", "value": "cv", "type": "text"}, {"key": "file", "type": "file", "src": []}, {"key": "background", "value": "true", "type": "text"}, {"key": "webhook_url", "value": "{{base_url}}/webhook/test", "type": "text"}]}, "url": {"raw": "{{base_url}}/process", "host": ["{{base_url}}"], "path": ["process"]}, "description": "Test background processing with file upload. Select a PDF or DOCX file to upload."}, "response": []}]}