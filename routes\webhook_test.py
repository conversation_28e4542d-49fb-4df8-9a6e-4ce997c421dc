import json
from datetime import datetime
from fastapi import APIRouter, Request, Body
from typing import Dict, Any

router = APIRouter()


@router.post("/webhook/test", summary="Test webhook endpoint for development")
async def test_webhook_endpoint(
    request: Request,
    payload: Dict[str, Any] = Body(...)
):
    """
    Simple test webhook endpoint to verify webhook functionality.
    
    This endpoint logs received webhooks and returns a confirmation.
    Use this URL when testing webhook notifications during development.
    
    Args:
        payload: Webhook payload from document processing
        
    Returns:
        dict: Confirmation of webhook receipt
        
    Example webhook payload:
    ```json
    {
        "task_id": "cv-1234567890-abc123",
        "status": "completed",
        "timestamp": "2024-01-15T10:30:00Z",
        "data": {
            "personal_info": {
                "full_name": "<PERSON>",
                "email": "<EMAIL>"
            }
        },
        "processing_time": 245.6
    }
    ```
    """
    logger = request.app.state.logger
    
    task_id = payload.get("task_id", "unknown")
    status = payload.get("status", "unknown")
    processing_time = payload.get("processing_time", 0)
    
    logger.info(f"🔔 Webhook received for task {task_id}")
    logger.info(f"   Status: {status}")
    logger.info(f"   Processing time: {processing_time}s")
    logger.info(f"   Full payload: {json.dumps(payload, indent=2)}")
    
    return {
        "status": "received",
        "message": f"Webhook for task {task_id} received successfully",
        "received_at": datetime.utcnow().isoformat(),
        "task_id": task_id,
        "task_status": status,
        "processing_time": processing_time
    }


@router.get("/webhook/test-url", summary="Get test webhook URL")
async def get_test_webhook_url(request: Request):
    """
    Get the test webhook URL for development.
    
    Returns the full URL that can be used as webhook_url parameter
    when submitting documents for processing.
    """
    # Get the base URL from the request
    base_url = str(request.base_url).rstrip('/')
    webhook_url = f"{base_url}/webhook/test"
    
    return {
        "webhook_url": webhook_url,
        "usage": f"Use this URL as webhook_url parameter when calling /process",
        "example": f'curl -X POST "http://localhost:8000/process" -F "action=cv" -F "file=@resume.pdf" -F "webhook_url={webhook_url}"'
    }
