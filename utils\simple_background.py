import asyncio
import time
import uuid
import tempfile
import os
from typing import Optional, Dict, Any
from fastapi import UploadFile
from utils.webhook_sender import webhook_sender


class SimpleBackgroundProcessor:
    """
    Minimal background processor that wraps your existing processing logic.
    Uses in-memory storage and asyncio tasks - no external dependencies.
    """
    
    def __init__(self):
        # Simple in-memory task storage
        self.tasks: Dict[str, Dict[str, Any]] = {}
    
    def generate_task_id(self, action: str) -> str:
        """Generate unique task ID."""
        timestamp = int(time.time())
        unique_id = uuid.uuid4().hex[:8]
        return f"{action}-{timestamp}-{unique_id}"
    
    async def submit_task(
        self, 
        action: str,
        processor,
        file: Optional[UploadFile] = None,
        data: Optional[str] = None,
        webhook_url: Optional[str] = None,
        logger = None
    ) -> str:
        """
        Submit task for background processing using your existing processor logic.
        
        Returns task_id immediately, processes in background.
        """
        task_id = self.generate_task_id(action)
        
        # Store initial task info
        self.tasks[task_id] = {
            "task_id": task_id,
            "action": action,
            "status": "processing",
            "created_at": time.time(),
            "webhook_url": webhook_url,
            "file_name": getattr(file, "filename", None) if file else str(data)[:50] if data else "No file",
            "result": None,
            "error": None,
            "processing_time": None
        }
        
        # Handle file for background processing
        file_path = None
        if file:
            # Save uploaded file to temp location
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file.filename}")
            content = await file.read()
            temp_file.write(content)
            temp_file.close()
            file_path = temp_file.name
        
        # Start background processing (don't await)
        asyncio.create_task(
            self._process_in_background(task_id, processor, file_path, data, webhook_url, logger)
        )
        
        if logger:
            logger.info(f"Task {task_id} submitted for background processing")
        
        return task_id
    
    async def _process_in_background(
        self, 
        task_id: str, 
        processor, 
        file_path: Optional[str],
        data: Optional[str],
        webhook_url: Optional[str],
        logger
    ):
        """
        Process document in background using your existing processor.
        This is where your original processing logic runs.
        """
        start_time = time.time()
        
        try:
            if logger:
                logger.info(f"Starting background processing for task {task_id}")
            
            # Recreate file object if needed
            file_obj = None
            if file_path:
                class TempUploadFile:
                    def __init__(self, path: str):
                        self.file_path = path
                        self.filename = os.path.basename(path)
                        self.content_type = "application/octet-stream"
                    
                    async def read(self):
                        with open(self.file_path, 'rb') as f:
                            return f.read()
                
                file_obj = TempUploadFile(file_path)
            
            # Use your existing processor logic - NO CHANGES NEEDED
            result = await processor.process(file_obj, data)
            processing_time = time.time() - start_time
            
            # Update task with success
            self.tasks[task_id].update({
                "status": "completed",
                "result": result,
                "processing_time": processing_time,
                "completed_at": time.time()
            })
            
            # Send success webhook
            if webhook_url:
                webhook_sender.send_webhook_background(
                    webhook_url=webhook_url,
                    task_id=task_id,
                    status="completed",
                    data=result,
                    processing_time=processing_time
                )
            
            if logger:
                logger.info(f"Task {task_id} completed successfully in {processing_time:.2f}s")
        
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = str(e)
            
            # Update task with failure
            self.tasks[task_id].update({
                "status": "failed",
                "error": error_msg,
                "processing_time": processing_time,
                "completed_at": time.time()
            })
            
            # Send failure webhook
            if webhook_url:
                webhook_sender.send_webhook_background(
                    webhook_url=webhook_url,
                    task_id=task_id,
                    status="failed",
                    data={"error": error_msg},
                    processing_time=processing_time
                )
            
            if logger:
                logger.error(f"Task {task_id} failed after {processing_time:.2f}s: {error_msg}")
        
        finally:
            # Clean up temp file
            if file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except Exception as cleanup_error:
                    if logger:
                        logger.warning(f"Could not remove temp file {file_path}: {cleanup_error}")
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task status and basic info."""
        return self.tasks.get(task_id)
    
    def get_task_result(self, task_id: str) -> Optional[Any]:
        """Get task result if completed."""
        task = self.tasks.get(task_id)
        if not task:
            return None
        
        if task["status"] == "completed":
            return task["result"]
        elif task["status"] == "failed":
            return {"error": task["error"]}
        else:
            return None  # Still processing
    
    def list_tasks(self) -> Dict[str, Any]:
        """List all tasks for monitoring."""
        active = [task for task in self.tasks.values() if task["status"] == "processing"]
        completed = [task for task in self.tasks.values() if task["status"] in ["completed", "failed"]]
        
        return {
            "active": active,
            "completed": completed,
            "active_count": len(active),
            "completed_count": len(completed),
            "total_count": len(self.tasks)
        }
    
    def cleanup_old_tasks(self, max_age_hours: int = 24) -> int:
        """Clean up old completed tasks."""
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        to_remove = []
        for task_id, task in self.tasks.items():
            if (task["status"] in ["completed", "failed"] and 
                "completed_at" in task and
                current_time - task["completed_at"] > max_age_seconds):
                to_remove.append(task_id)
        
        for task_id in to_remove:
            del self.tasks[task_id]
        
        return len(to_remove)


# Global instance
simple_background = SimpleBackgroundProcessor()
